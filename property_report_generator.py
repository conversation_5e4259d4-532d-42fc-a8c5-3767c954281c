#!/usr/bin/env python3
"""
PropertyGuru Real Estate Data Analysis Report Generator
Creates comprehensive HTML report with analysis and insights
"""

import json
import re
from datetime import datetime
from collections import Counter

def load_data(file_path):
    """Load JSON data"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data['listings'], data['scraping_stats']

def clean_price(price_str):
    """Clean price string and convert to numeric"""
    if not price_str or price_str == "":
        return None
    price_clean = re.sub(r'[S$,\s]', '', price_str)
    try:
        return float(price_clean)
    except:
        return None

def clean_area(area_str):
    """Clean area string and convert to numeric"""
    if not area_str or area_str == "":
        return None
    area_match = re.search(r'([\d,]+)', area_str)
    if area_match:
        area_clean = area_match.group(1).replace(',', '')
        try:
            return float(area_clean)
        except:
            return None
    return None

def extract_mrt_info(address_str):
    """Extract MRT station and distance information"""
    if not address_str:
        return None, None, None
    
    min_match = re.search(r'(\d+)\s*min', address_str)
    minutes = int(min_match.group(1)) if min_match else None
    
    distance_match = re.search(r'\(([\d.]+)\s*(m|km)\)', address_str)
    distance = None
    if distance_match:
        dist_val = float(distance_match.group(1))
        unit = distance_match.group(2)
        distance = dist_val if unit == 'm' else dist_val * 1000
    
    mrt_match = re.search(r'from\s+([A-Z0-9]+)\s+(.+?)\s+MRT\s+Station', address_str)
    mrt_station = mrt_match.group(2) if mrt_match else None
    
    return minutes, distance, mrt_station

def process_data(listings):
    """Process and clean the data"""
    for listing in listings:
        listing['price_numeric'] = clean_price(listing.get('price', ''))
        listing['area_numeric'] = clean_area(listing.get('area', ''))
        
        if listing['price_numeric'] and listing['area_numeric']:
            listing['price_per_sqft'] = listing['price_numeric'] / listing['area_numeric']
        else:
            listing['price_per_sqft'] = None
            
        mrt_info = extract_mrt_info(listing.get('address', ''))
        listing['mrt_minutes'] = mrt_info[0]
        listing['mrt_distance_m'] = mrt_info[1]
        listing['mrt_station'] = mrt_info[2]
    
    return [l for l in listings if l['price_numeric'] and l['area_numeric']]

def generate_html_report(listings, stats):
    """Generate comprehensive HTML report"""
    
    # Calculate statistics
    prices = [l['price_numeric'] for l in listings if l['price_numeric']]
    areas = [l['area_numeric'] for l in listings if l['area_numeric']]
    psf_values = [l['price_per_sqft'] for l in listings if l['price_per_sqft']]
    mrt_minutes = [l['mrt_minutes'] for l in listings if l['mrt_minutes']]
    
    # Price statistics
    mean_price = sum(prices) / len(prices)
    sorted_prices = sorted(prices)
    median_price = sorted_prices[len(sorted_prices)//2]
    
    # Area statistics
    mean_area = sum(areas) / len(areas)
    sorted_areas = sorted(areas)
    median_area = sorted_areas[len(sorted_areas)//2]
    
    # PSF statistics
    mean_psf = sum(psf_values) / len(psf_values)
    sorted_psf = sorted(psf_values)
    median_psf = sorted_psf[len(sorted_psf)//2]
    
    # MRT statistics
    mean_mrt = sum(mrt_minutes) / len(mrt_minutes) if mrt_minutes else 0
    
    # Popular developments
    titles = [l['title'] for l in listings if l['title']]
    title_counts = Counter(titles)
    top_developments = title_counts.most_common(10)
    
    # MRT stations
    mrt_stations = [l['mrt_station'] for l in listings if l['mrt_station']]
    mrt_counts = Counter(mrt_stations)
    top_mrt = mrt_counts.most_common(6)
    
    # Price ranges
    price_ranges = {
        'Under $2M': sum(1 for p in prices if p < 2000000),
        '$2M-$2.3M': sum(1 for p in prices if 2000000 <= p < 2300000),
        '$2.3M-$2.5M': sum(1 for p in prices if 2300000 <= p < 2500000),
        'Above $2.5M': sum(1 for p in prices if p >= 2500000)
    }
    
    # Area ranges
    area_ranges = {
        'Compact (<1000)': sum(1 for a in areas if a < 1000),
        'Medium (1000-1300)': sum(1 for a in areas if 1000 <= a < 1300),
        'Large (1300-1600)': sum(1 for a in areas if 1300 <= a < 1600),
        'Extra Large (>1600)': sum(1 for a in areas if a >= 1600)
    }
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PropertyGuru Singapore Real Estate Market Analysis Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
            margin-top: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }}
        .stat-card .value {{
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }}
        .section {{
            margin: 40px 0;
            padding: 20px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }}
        .section h2 {{
            color: #2c3e50;
            margin-top: 0;
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .data-table th, .data-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        .data-table th {{
            background-color: #34495e;
            color: white;
        }}
        .data-table tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        .insight-box {{
            background-color: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 15px;
            margin: 20px 0;
        }}
        .insight-box h4 {{
            color: #27ae60;
            margin-top: 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PropertyGuru Singapore Real Estate Market Analysis</h1>
            <div class="subtitle">Comprehensive Market Report - {datetime.now().strftime('%B %d, %Y')}</div>
            <div class="subtitle">Data Period: {stats['start_time'][:10]} to {stats['end_time'][:10]}</div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>Total Properties</h3>
                <div class="value">{len(listings)}</div>
            </div>
            <div class="stat-card">
                <h3>Average Price</h3>
                <div class="value">${mean_price:,.0f}</div>
            </div>
            <div class="stat-card">
                <h3>Average Area</h3>
                <div class="value">{mean_area:.0f} sqft</div>
            </div>
            <div class="stat-card">
                <h3>Average PSF</h3>
                <div class="value">${mean_psf:.0f}</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Price Analysis</h2>
            <div class="chart-container">
                <h3>Price Distribution</h3>
                <table class="data-table">
                    <tr><th>Price Range</th><th>Count</th><th>Percentage</th></tr>"""
    
    for range_name, count in price_ranges.items():
        percentage = (count / len(prices)) * 100
        html_content += f"<tr><td>{range_name}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
    
    html_content += f"""
                </table>
            </div>
            <div class="insight-box">
                <h4>💡 Key Insights</h4>
                <p>• The median price of ${median_price:,.0f} indicates a balanced market</p>
                <p>• {price_ranges['$2M-$2.3M']} properties ({price_ranges['$2M-$2.3M']/len(prices)*100:.1f}%) fall in the $2M-$2.3M range</p>
                <p>• Premium properties above $2.5M represent {price_ranges['Above $2.5M']/len(prices)*100:.1f}% of the market</p>
            </div>
        </div>

        <div class="section">
            <h2>🏠 Property Size Analysis</h2>
            <div class="chart-container">
                <h3>Area Distribution</h3>
                <table class="data-table">
                    <tr><th>Size Category</th><th>Count</th><th>Percentage</th></tr>"""
    
    for range_name, count in area_ranges.items():
        percentage = (count / len(areas)) * 100
        html_content += f"<tr><td>{range_name}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
    
    html_content += f"""
                </table>
            </div>
            <div class="insight-box">
                <h4>💡 Key Insights</h4>
                <p>• Most properties ({area_ranges['Medium (1000-1300)'] + area_ranges['Large (1300-1600)']} units, {(area_ranges['Medium (1000-1300)'] + area_ranges['Large (1300-1600)'])/len(areas)*100:.1f}%) are medium to large sized</p>
                <p>• Compact units under 1000 sqft represent {area_ranges['Compact (<1000)']/len(areas)*100:.1f}% of listings</p>
                <p>• Extra large properties over 1600 sqft are rare at {area_ranges['Extra Large (>1600)']/len(areas)*100:.1f}%</p>
            </div>
        </div>

        <div class="section">
            <h2>🚇 Location & MRT Accessibility</h2>
            <div class="chart-container">
                <h3>Top MRT Stations</h3>
                <table class="data-table">
                    <tr><th>MRT Station</th><th>Properties</th><th>Market Share</th></tr>"""
    
    for station, count in top_mrt:
        percentage = (count / len(listings)) * 100
        html_content += f"<tr><td>{station}</td><td>{count}</td><td>{percentage:.1f}%</td></tr>"
    
    within_5 = sum(1 for m in mrt_minutes if m <= 5)
    within_10 = sum(1 for m in mrt_minutes if m <= 10)
    within_15 = sum(1 for m in mrt_minutes if m <= 15)
    
    html_content += f"""
                </table>
            </div>
            <div class="insight-box">
                <h4>💡 Key Insights</h4>
                <p>• Average walking time to MRT: {mean_mrt:.1f} minutes</p>
                <p>• {within_5} properties ({within_5/len(mrt_minutes)*100:.1f}%) are within 5 minutes walk</p>
                <p>• {within_10} properties ({within_10/len(mrt_minutes)*100:.1f}%) are within 10 minutes walk</p>
                <p>• Serangoon is the most popular area with {mrt_counts['Serangoon']} properties</p>
            </div>
        </div>

        <div class="section">
            <h2>🏢 Popular Developments</h2>
            <div class="chart-container">
                <h3>Top 10 Developments by Listings</h3>
                <table class="data-table">
                    <tr><th>Development</th><th>Listings</th><th>Avg Price</th></tr>"""
    
    for title, count in top_developments:
        if count > 1:
            dev_prices = [l['price_numeric'] for l in listings if l['title'] == title and l['price_numeric']]
            avg_price = sum(dev_prices) / len(dev_prices) if dev_prices else 0
            html_content += f"<tr><td>{title}</td><td>{count}</td><td>${avg_price:,.0f}</td></tr>"
    
    html_content += f"""
                </table>
            </div>
        </div>

        <div class="section">
            <h2>💰 Price per Square Foot Analysis</h2>
            <div class="chart-container">
                <p><strong>PSF Statistics:</strong></p>
                <ul>
                    <li>Mean PSF: ${mean_psf:.0f}</li>
                    <li>Median PSF: ${median_psf:.0f}</li>
                    <li>Min PSF: ${min(psf_values):.0f}</li>
                    <li>Max PSF: ${max(psf_values):.0f}</li>
                </ul>
            </div>
            <div class="insight-box">
                <h4>💡 Market Summary</h4>
                <p>• The Singapore condo market shows strong demand in prime locations</p>
                <p>• Properties near Serangoon, Toa Payoh, and Lorong Chuan MRT stations dominate listings</p>
                <p>• Price per square foot varies significantly, indicating diverse market segments</p>
                <p>• Most properties offer good MRT accessibility within 15 minutes walk</p>
            </div>
        </div>

        <div class="footer">
            <p>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Data source: PropertyGuru Singapore | Analysis period: {stats['start_time'][:10]} to {stats['end_time'][:10]}</p>
        </div>
    </div>
</body>
</html>"""
    
    return html_content

def main():
    """Main function"""
    # Load and process data
    listings, stats = load_data('propertyguru_scraper_project/output/propertyguru_complete_20250610_121349.json')
    clean_listings = process_data(listings)
    
    # Generate HTML report
    html_report = generate_html_report(clean_listings, stats)
    
    # Save report
    with open('PropertyGuru_Market_Analysis_Report.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print("✅ Comprehensive HTML report generated: PropertyGuru_Market_Analysis_Report.html")
    print(f"📊 Analyzed {len(clean_listings)} properties from {len(listings)} total listings")

if __name__ == "__main__":
    main()
