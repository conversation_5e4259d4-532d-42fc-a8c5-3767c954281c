#!/usr/bin/env python3
"""
PropertyGuru Real Estate Data Analysis Report
Analyzes property listings data from Singapore real estate market
"""

import json
import re
from datetime import datetime
from collections import Counter

def load_data(file_path):
    """Load JSON data"""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    return data['listings'], data['scraping_stats']

def clean_price(price_str):
    """Clean price string and convert to numeric"""
    if not price_str or price_str == "":
        return None
    # Remove 'S$', commas, and spaces
    price_clean = re.sub(r'[S$,\s]', '', price_str)
    try:
        return float(price_clean)
    except:
        return None

def clean_area(area_str):
    """Clean area string and convert to numeric"""
    if not area_str or area_str == "":
        return None
    # Extract numeric value from area string
    area_match = re.search(r'([\d,]+)', area_str)
    if area_match:
        area_clean = area_match.group(1).replace(',', '')
        try:
            return float(area_clean)
        except:
            return None
    return None

def extract_mrt_info(address_str):
    """Extract MRT station and distance information"""
    if not address_str:
        return None, None, None
    
    # Extract minutes
    min_match = re.search(r'(\d+)\s*min', address_str)
    minutes = int(min_match.group(1)) if min_match else None
    
    # Extract distance in meters/km
    distance_match = re.search(r'\(([\d.]+)\s*(m|km)\)', address_str)
    distance = None
    if distance_match:
        dist_val = float(distance_match.group(1))
        unit = distance_match.group(2)
        distance = dist_val if unit == 'm' else dist_val * 1000
    
    # Extract MRT station
    mrt_match = re.search(r'from\s+([A-Z0-9]+)\s+(.+?)\s+MRT\s+Station', address_str)
    mrt_station = mrt_match.group(2) if mrt_match else None
    mrt_code = mrt_match.group(1) if mrt_match else None
    
    return minutes, distance, mrt_station

def analyze_data(listings):
    """Perform comprehensive data analysis"""
    print("=" * 80)
    print("PROPERTYGURU SINGAPORE REAL ESTATE MARKET ANALYSIS REPORT")
    print("=" * 80)
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total Properties Analyzed: {len(listings)}")
    print()

    # Data cleaning - add computed fields to each listing
    for listing in listings:
        listing['price_numeric'] = clean_price(listing.get('price', ''))
        listing['area_numeric'] = clean_area(listing.get('area', ''))

        # Calculate price per sqft
        if listing['price_numeric'] and listing['area_numeric']:
            listing['price_per_sqft'] = listing['price_numeric'] / listing['area_numeric']
        else:
            listing['price_per_sqft'] = None

        # Extract MRT information
        mrt_info = extract_mrt_info(listing.get('address', ''))
        listing['mrt_minutes'] = mrt_info[0]
        listing['mrt_distance_m'] = mrt_info[1]
        listing['mrt_station'] = mrt_info[2]

    # Filter out listings without price and area data
    clean_listings = [l for l in listings if l['price_numeric'] and l['area_numeric']]

    return clean_listings

def price_analysis(listings):
    """Analyze price distribution and statistics"""
    print("1. PRICE ANALYSIS")
    print("-" * 50)

    prices = [l['price_numeric'] for l in listings if l['price_numeric']]

    if prices:
        mean_price = sum(prices) / len(prices)
        sorted_prices = sorted(prices)
        median_price = sorted_prices[len(sorted_prices)//2]
        min_price = min(prices)
        max_price = max(prices)

        # Calculate standard deviation
        variance = sum((p - mean_price) ** 2 for p in prices) / len(prices)
        std_dev = variance ** 0.5

        print(f"Price Statistics (SGD):")
        print(f"  Mean Price: ${mean_price:,.0f}")
        print(f"  Median Price: ${median_price:,.0f}")
        print(f"  Min Price: ${min_price:,.0f}")
        print(f"  Max Price: ${max_price:,.0f}")
        print(f"  Standard Deviation: ${std_dev:,.0f}")
        print()

        # Price ranges
        ranges = {
            'Under $2M': sum(1 for p in prices if p < 2000000),
            '$2M-$2.3M': sum(1 for p in prices if 2000000 <= p < 2300000),
            '$2.3M-$2.5M': sum(1 for p in prices if 2300000 <= p < 2500000),
            'Above $2.5M': sum(1 for p in prices if p >= 2500000)
        }

        print("Price Distribution by Ranges:")
        for range_name, count in ranges.items():
            percentage = (count / len(prices)) * 100
            print(f"  {range_name}: {count} properties ({percentage:.1f}%)")
        print()

def area_analysis(listings):
    """Analyze property area distribution"""
    print("2. PROPERTY AREA ANALYSIS")
    print("-" * 50)

    areas = [l['area_numeric'] for l in listings if l['area_numeric']]

    if areas:
        mean_area = sum(areas) / len(areas)
        sorted_areas = sorted(areas)
        median_area = sorted_areas[len(sorted_areas)//2]
        min_area = min(areas)
        max_area = max(areas)

        print(f"Area Statistics (sqft):")
        print(f"  Mean Area: {mean_area:.0f} sqft")
        print(f"  Median Area: {median_area:.0f} sqft")
        print(f"  Min Area: {min_area:.0f} sqft")
        print(f"  Max Area: {max_area:.0f} sqft")
        print()

        # Area ranges
        ranges = {
            'Compact (<1000)': sum(1 for a in areas if a < 1000),
            'Medium (1000-1300)': sum(1 for a in areas if 1000 <= a < 1300),
            'Large (1300-1600)': sum(1 for a in areas if 1300 <= a < 1600),
            'Extra Large (>1600)': sum(1 for a in areas if a >= 1600)
        }

        print("Area Distribution by Size:")
        for range_name, count in ranges.items():
            percentage = (count / len(areas)) * 100
            print(f"  {range_name}: {count} properties ({percentage:.1f}%)")
        print()

def location_analysis(listings):
    """Analyze location and MRT accessibility"""
    print("3. LOCATION & MRT ACCESSIBILITY ANALYSIS")
    print("-" * 50)

    # MRT station popularity
    mrt_stations = [l['mrt_station'] for l in listings if l['mrt_station']]
    mrt_counts = Counter(mrt_stations)

    print("Top 10 Most Popular MRT Stations:")
    for station, count in mrt_counts.most_common(10):
        percentage = (count / len(listings)) * 100
        print(f"  {station}: {count} properties ({percentage:.1f}%)")
    print()

    # Distance to MRT analysis
    mrt_minutes = [l['mrt_minutes'] for l in listings if l['mrt_minutes']]
    if mrt_minutes:
        mean_minutes = sum(mrt_minutes) / len(mrt_minutes)
        sorted_minutes = sorted(mrt_minutes)
        median_minutes = sorted_minutes[len(sorted_minutes)//2]

        within_5 = sum(1 for m in mrt_minutes if m <= 5)
        within_10 = sum(1 for m in mrt_minutes if m <= 10)
        within_15 = sum(1 for m in mrt_minutes if m <= 15)

        print(f"MRT Accessibility (Walking Time):")
        print(f"  Average: {mean_minutes:.1f} minutes")
        print(f"  Median: {median_minutes:.1f} minutes")
        print(f"  Properties within 5 min: {within_5} ({within_5/len(mrt_minutes)*100:.1f}%)")
        print(f"  Properties within 10 min: {within_10} ({within_10/len(mrt_minutes)*100:.1f}%)")
        print(f"  Properties within 15 min: {within_15} ({within_15/len(mrt_minutes)*100:.1f}%)")
    print()

def development_analysis(listings):
    """Analyze popular developments"""
    print("4. POPULAR DEVELOPMENTS ANALYSIS")
    print("-" * 50)

    # Top developments by number of listings
    titles = [l['title'] for l in listings if l['title']]
    title_counts = Counter(titles)

    print("Top 15 Developments by Number of Listings:")
    for title, count in title_counts.most_common(15):
        if count > 1:  # Only show developments with multiple listings
            # Calculate average price for this development
            dev_prices = [l['price_numeric'] for l in listings if l['title'] == title and l['price_numeric']]
            if dev_prices:
                avg_price = sum(dev_prices) / len(dev_prices)
                print(f"  {title}: {count} listings (Avg: ${avg_price:,.0f})")
    print()

def price_per_sqft_analysis(listings):
    """Analyze price per square foot"""
    print("5. PRICE PER SQUARE FOOT ANALYSIS")
    print("-" * 50)

    psf_values = [l['price_per_sqft'] for l in listings if l['price_per_sqft']]

    if psf_values:
        mean_psf = sum(psf_values) / len(psf_values)
        sorted_psf = sorted(psf_values)
        median_psf = sorted_psf[len(sorted_psf)//2]
        min_psf = min(psf_values)
        max_psf = max(psf_values)

        print(f"Price per sqft Statistics (SGD):")
        print(f"  Mean PSF: ${mean_psf:.0f}")
        print(f"  Median PSF: ${median_psf:.0f}")
        print(f"  Min PSF: ${min_psf:.0f}")
        print(f"  Max PSF: ${max_psf:.0f}")
        print()

        # PSF by development (top 10 with multiple listings)
        dev_psf = {}
        for listing in listings:
            if listing['title'] and listing['price_per_sqft']:
                title = listing['title']
                if title not in dev_psf:
                    dev_psf[title] = []
                dev_psf[title].append(listing['price_per_sqft'])

        # Calculate average PSF for developments with multiple listings
        dev_avg_psf = {}
        for title, psf_list in dev_psf.items():
            if len(psf_list) > 1:
                dev_avg_psf[title] = (sum(psf_list) / len(psf_list), len(psf_list))

        # Sort by PSF and get top 10
        sorted_dev_psf = sorted(dev_avg_psf.items(), key=lambda x: x[1][0], reverse=True)[:10]

        print("Top 10 Developments by Price per sqft (multiple listings only):")
        for title, (avg_psf, count) in sorted_dev_psf:
            print(f"  {title}: ${avg_psf:.0f}/sqft ({count} listings)")
        print()

def main():
    """Main analysis function"""
    # Load data
    listings, stats = load_data('propertyguru_scraper_project/output/propertyguru_complete_20250610_121349.json')

    print(f"Data Collection Summary:")
    print(f"  Scraping Period: {stats['start_time']} to {stats['end_time']}")
    print(f"  Pages Scraped: {stats['successful_pages']}/{stats['total_pages']}")
    print(f"  Total Listings: {stats['total_listings']}")
    print()

    # Clean and analyze data
    clean_listings = analyze_data(listings)

    # Perform various analyses
    price_analysis(clean_listings)
    area_analysis(clean_listings)
    location_analysis(clean_listings)
    development_analysis(clean_listings)
    price_per_sqft_analysis(clean_listings)

    print("=" * 80)
    print("ANALYSIS COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    main()
